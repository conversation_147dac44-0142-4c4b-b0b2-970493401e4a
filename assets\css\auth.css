/* أنماط صفحات المصادقة - نقرة هوست */

.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="auth-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23auth-pattern)"/></svg>');
    opacity: 0.3;
}

.auth-container {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 2rem 0;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.auth-logo img {
    height: 50px;
    width: auto;
}

.auth-logo h2 {
    color: var(--primary-color);
    font-weight: 700;
    margin: 0;
    font-size: 1.8rem;
}

.auth-header h3 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.auth-header p {
    color: var(--gray-color);
    margin: 0;
    font-size: 1rem;
}

.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: block;
}

.input-group {
    position: relative;
    display: flex;
    align-items: stretch;
}

.input-group-text {
    background: var(--light-color);
    border: 2px solid #e5e7eb;
    border-left: none;
    color: var(--gray-color);
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    font-size: 1rem;
}

.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    outline: none;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.toggle-password {
    border: 2px solid #e5e7eb;
    border-right: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    background: var(--light-color);
    color: var(--gray-color);
    padding: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.toggle-password:hover {
    background: #e5e7eb;
    color: var(--dark-color);
}

.form-check-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    width: 1.2rem;
    height: 1.2rem;
    border: 2px solid #e5e7eb;
    border-radius: 4px;
    background: var(--white-color);
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: var(--dark-color);
    font-weight: 500;
    cursor: pointer;
    margin: 0;
}

.forgot-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-link:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border: none;
    color: var(--white-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
}

.auth-footer {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.auth-footer p {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.auth-footer a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.auth-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.auth-links a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.auth-links a:hover {
    color: var(--primary-color);
}

.alert {
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-right: 4px solid #dc2626;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border-right: 4px solid #059669;
}

.form-text {
    font-size: 0.875rem;
    color: var(--gray-color);
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
        border-radius: 15px;
    }
    
    .auth-logo {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .auth-logo h2 {
        font-size: 1.5rem;
    }
    
    .auth-header h3 {
        font-size: 1.3rem;
    }
    
    .form-check-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .auth-links {
        flex-direction: column;
        gap: 1rem;
    }
    
    .input-group {
        flex-wrap: wrap;
    }
    
    .input-group-text,
    .form-control,
    .toggle-password {
        border-radius: var(--border-radius);
        border: 2px solid #e5e7eb;
    }
    
    .form-control {
        margin-top: 0.5rem;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .auth-container {
        padding: 1rem 0;
    }
    
    .auth-card {
        padding: 1.5rem 1rem;
        margin: 0.5rem;
    }
    
    .auth-header {
        margin-bottom: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    .btn-lg {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

/* Animation Effects */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    animation: slideInUp 0.6s ease-out;
}

/* Loading State */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .auth-card {
        background: rgba(31, 41, 55, 0.95);
        color: var(--white-color);
    }
    
    .auth-header h3,
    .form-group label,
    .form-check-label {
        color: var(--white-color);
    }
    
    .form-control {
        background: rgba(55, 65, 81, 0.8);
        border-color: #4b5563;
        color: var(--white-color);
    }
    
    .form-control:focus {
        background: rgba(55, 65, 81, 1);
    }
    
    .input-group-text,
    .toggle-password {
        background: rgba(55, 65, 81, 0.8);
        border-color: #4b5563;
        color: #d1d5db;
    }
}
