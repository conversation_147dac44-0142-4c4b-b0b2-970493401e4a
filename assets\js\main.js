/**
 * ملف JavaScript الرئيسي - نقرة هوست
 * Main JavaScript File - Nakra Host
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // تهيئة المتغيرات
    const navbar = document.querySelector('.navbar');
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    const heroSection = document.querySelector('.hero-section');
    
    // تأثير تمرير الصفحة على شريط التنقل
    function handleNavbarScroll() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
    
    // إضافة مستمع للتمرير
    window.addEventListener('scroll', handleNavbarScroll);
    
    // التمرير السلس للروابط الداخلية
    function initSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');
        
        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    const headerHeight = navbar.offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    
                    // إغلاق القائمة في الأجهزة المحمولة
                    if (navbarCollapse.classList.contains('show')) {
                        navbarToggler.click();
                    }
                }
            });
        });
    }
    
    // تهيئة التمرير السلس
    initSmoothScrolling();
    
    // تأثيرات الظهور عند التمرير
    function initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        // مراقبة العناصر
        const elementsToAnimate = document.querySelectorAll('.feature-card, .section-title, .section-subtitle');
        elementsToAnimate.forEach(element => {
            observer.observe(element);
        });
    }
    
    // تهيئة تأثيرات الظهور
    initScrollAnimations();
    
    // معالج النماذج
    function initFormHandlers() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // التحقق من صحة البيانات
                if (validateForm(this)) {
                    // إرسال النموذج
                    submitForm(this);
                }
            });
        });
    }
    
    // التحقق من صحة النموذج
    function validateForm(form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                clearFieldError(field);
                
                // التحقق من البريد الإلكتروني
                if (field.type === 'email' && !isValidEmail(field.value)) {
                    showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
                    isValid = false;
                }
                
                // التحقق من رقم الهاتف
                if (field.type === 'tel' && !isValidPhone(field.value)) {
                    showFieldError(field, 'يرجى إدخال رقم هاتف صحيح');
                    isValid = false;
                }
            }
        });
        
        return isValid;
    }
    
    // عرض خطأ الحقل
    function showFieldError(field, message) {
        clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }
    
    // إزالة خطأ الحقل
    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    // التحقق من صحة البريد الإلكتروني
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // التحقق من صحة رقم الهاتف
    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        return phoneRegex.test(phone);
    }
    
    // إرسال النموذج
    function submitForm(form) {
        const submitBtn = form.querySelector('[type="submit"]');
        const originalText = submitBtn.textContent;
        
        // تعطيل الزر وإظهار حالة التحميل
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        
        // جمع بيانات النموذج
        const formData = new FormData(form);
        
        // إرسال البيانات
        fetch(form.action || 'process_form.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إرسال النموذج بنجاح!', 'success');
                form.reset();
            } else {
                showAlert(data.message || 'حدث خطأ أثناء الإرسال', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في الاتصال', 'error');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    }
    
    // عرض التنبيهات
    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // إضافة التنبيه إلى أعلى الصفحة
        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(alertDiv, container.firstChild);
        }
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    // تهيئة معالجات النماذج
    initFormHandlers();
    
    // معالج تحميل الصور
    function initImageLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
    
    // تهيئة تحميل الصور الكسول
    initImageLazyLoading();
    
    // معالج الوضع المظلم
    function initDarkModeToggle() {
        const darkModeToggle = document.querySelector('#darkModeToggle');
        
        if (darkModeToggle) {
            // التحقق من الإعداد المحفوظ
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-mode');
                darkModeToggle.checked = true;
            }
            
            darkModeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-mode');
                    localStorage.setItem('theme', 'dark');
                } else {
                    document.body.classList.remove('dark-mode');
                    localStorage.setItem('theme', 'light');
                }
            });
        }
    }
    
    // تهيئة الوضع المظلم
    initDarkModeToggle();
    
    // معالج البحث المباشر
    function initLiveSearch() {
        const searchInput = document.querySelector('#liveSearch');
        
        if (searchInput) {
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                
                searchTimeout = setTimeout(() => {
                    const query = this.value.trim();
                    
                    if (query.length >= 2) {
                        performSearch(query);
                    } else {
                        clearSearchResults();
                    }
                }, 300);
            });
        }
    }
    
    // تنفيذ البحث
    function performSearch(query) {
        fetch(`search.php?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data.results);
            })
            .catch(error => {
                console.error('Search error:', error);
            });
    }
    
    // عرض نتائج البحث
    function displaySearchResults(results) {
        const resultsContainer = document.querySelector('#searchResults');
        
        if (resultsContainer) {
            if (results.length > 0) {
                resultsContainer.innerHTML = results.map(result => `
                    <div class="search-result-item">
                        <h6>${result.title}</h6>
                        <p>${result.description}</p>
                        <a href="${result.url}" class="btn btn-sm btn-outline-primary">عرض التفاصيل</a>
                    </div>
                `).join('');
                resultsContainer.style.display = 'block';
            } else {
                resultsContainer.innerHTML = '<p class="text-muted">لا توجد نتائج</p>';
                resultsContainer.style.display = 'block';
            }
        }
    }
    
    // مسح نتائج البحث
    function clearSearchResults() {
        const resultsContainer = document.querySelector('#searchResults');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }
    
    // تهيئة البحث المباشر
    initLiveSearch();
    
    // إضافة أنماط CSS للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        .navbar.scrolled {
            background: rgba(37, 99, 235, 0.95) !important;
            backdrop-filter: blur(10px);
        }
        
        .lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .lazy.loaded {
            opacity: 1;
        }
        
        .search-result-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        #searchResults {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 400px;
            overflow-y: auto;
        }
    `;
    document.head.appendChild(style);
    
    console.log('نقرة هوست - تم تحميل الموقع بنجاح');
});
