<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';

$auth = new Auth();

// التحقق من تسجيل الدخول
if (!$auth->isLoggedIn()) {
    header('Location: ../../login.php');
    exit;
}

$user = $auth->getCurrentUser();
$pageTitle = 'الفواتير';

// الحصول على الفواتير
$database = new Database();
$db = $database->getConnection();

// فلترة الفواتير
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

$where_clause = "WHERE user_id = :user_id";
$params = [':user_id' => $user['id']];

if ($status_filter) {
    $where_clause .= " AND status = :status";
    $params[':status'] = $status_filter;
}

// عدد الفواتير الإجمالي
$count_query = "SELECT COUNT(*) as total FROM invoices $where_clause";
$count_stmt = $db->prepare($count_query);
$count_stmt->execute($params);
$total_invoices = $count_stmt->fetch()['total'];
$total_pages = ceil($total_invoices / $limit);

// جلب الفواتير
$query = "SELECT i.*, o.domain_name 
          FROM invoices i 
          LEFT JOIN orders o ON i.order_id = o.id 
          $where_clause 
          ORDER BY i.created_at DESC 
          LIMIT :limit OFFSET :offset";

$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$invoices = $stmt->fetchAll();

// إحصائيات الفواتير
$stats_query = "SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN status = 'unpaid' THEN total_amount ELSE 0 END) as unpaid_amount,
                    COUNT(CASE WHEN status = 'unpaid' THEN 1 END) as unpaid_count
                FROM invoices WHERE user_id = :user_id";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->bindParam(':user_id', $user['id']);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

include '../includes/header.php';
?>

<div class="dashboard-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-file-invoice-dollar"></i>
                        الفواتير
                    </h1>
                    <p class="page-subtitle">إدارة ومتابعة جميع فواتيرك</p>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="../services/new-order.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        طلب خدمة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['total_count']; ?></h3>
                        <p>إجمالي الفواتير</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['paid_amount'], 0); ?> جنيه</h3>
                        <p>المبلغ المدفوع</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['unpaid_amount'], 0); ?> جنيه</h3>
                        <p>المبلغ المعلق</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['unpaid_count']; ?></h3>
                        <p>فواتير معلقة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="dashboard-card mb-4">
            <div class="card-body">
                <form method="GET" class="row align-items-end">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <label for="status" class="form-label">حالة الفاتورة</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="unpaid" <?php echo $status_filter === 'unpaid' ? 'selected' : ''; ?>>غير مدفوعة</option>
                            <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                            <option value="overdue" <?php echo $status_filter === 'overdue' ? 'selected' : ''; ?>>متأخرة</option>
                            <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                        </select>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <a href="invoices.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Invoices Table -->
        <div class="dashboard-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-list"></i>
                    قائمة الفواتير
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($invoices)): ?>
                    <div class="empty-state">
                        <i class="fas fa-file-invoice"></i>
                        <h5>لا توجد فواتير</h5>
                        <p>لم يتم العثور على أي فواتير بالمعايير المحددة</p>
                        <a href="../services/new-order.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            طلب خدمة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>الخدمة</th>
                                    <th>المبلغ</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($invoices as $invoice): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo $invoice['domain_name'] ? htmlspecialchars($invoice['domain_name']) : 'خدمة عامة'; ?>
                                        </td>
                                        <td><?php echo number_format($invoice['amount'], 2); ?> جنيه</td>
                                        <td><?php echo number_format($invoice['tax_amount'], 2); ?> جنيه</td>
                                        <td>
                                            <strong><?php echo number_format($invoice['total_amount'], 2); ?> جنيه</strong>
                                        </td>
                                        <td>
                                            <?php 
                                                $due_date = new DateTime($invoice['due_date']);
                                                $now = new DateTime();
                                                $is_overdue = $due_date < $now && $invoice['status'] === 'unpaid';
                                                echo $due_date->format('Y/m/d');
                                                if ($is_overdue) {
                                                    echo ' <span class="badge badge-danger">متأخرة</span>';
                                                }
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                                $status_classes = [
                                                    'paid' => 'success',
                                                    'unpaid' => 'warning',
                                                    'overdue' => 'danger',
                                                    'cancelled' => 'secondary'
                                                ];
                                                $status_labels = [
                                                    'paid' => 'مدفوعة',
                                                    'unpaid' => 'غير مدفوعة',
                                                    'overdue' => 'متأخرة',
                                                    'cancelled' => 'ملغية'
                                                ];
                                                $status = $is_overdue ? 'overdue' : $invoice['status'];
                                            ?>
                                            <span class="badge badge-<?php echo $status_classes[$status]; ?>">
                                                <?php echo $status_labels[$status]; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="invoice.php?id=<?php echo $invoice['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="عرض الفاتورة">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="invoice.php?id=<?php echo $invoice['id']; ?>&action=download" 
                                                   class="btn btn-sm btn-outline-secondary" 
                                                   title="تحميل PDF">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <?php if ($invoice['status'] === 'unpaid'): ?>
                                                    <a href="pay.php?invoice=<?php echo $invoice['id']; ?>" 
                                                       class="btn btn-sm btn-success" 
                                                       title="دفع الفاتورة">
                                                        <i class="fas fa-credit-card"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تنقل الصفحات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
