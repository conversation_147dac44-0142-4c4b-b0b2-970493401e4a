<?php
/**
 * معالج التثبيت - نقرة هوست
 * Installation Handler - Nakra Host
 */

// التحقق من وجود ملف التكوين
if (file_exists('config/config.php')) {
    die('الموقع مثبت بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف config/config.php أولاً.');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من متطلبات النظام
            $requirements_met = true;
            
            if (version_compare(PHP_VERSION, '7.4.0', '<')) {
                $error = 'يتطلب PHP 7.4 أو أحدث';
                $requirements_met = false;
            }
            
            if (!extension_loaded('pdo') || !extension_loaded('pdo_mysql')) {
                $error = 'يتطلب PDO و PDO MySQL';
                $requirements_met = false;
            }
            
            if (!extension_loaded('mbstring')) {
                $error = 'يتطلب mbstring extension';
                $requirements_met = false;
            }
            
            if ($requirements_met) {
                header('Location: install.php?step=2');
                exit;
            }
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $db_host = $_POST['db_host'];
            $db_name = $_POST['db_name'];
            $db_user = $_POST['db_user'];
            $db_pass = $_POST['db_pass'];
            
            try {
                $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$db_name`");
                
                // تنفيذ SQL Schema
                $sql = file_get_contents('database/schema.sql');
                $pdo->exec($sql);
                
                // حفظ إعدادات قاعدة البيانات
                $_SESSION['db_config'] = [
                    'host' => $db_host,
                    'name' => $db_name,
                    'user' => $db_user,
                    'pass' => $db_pass
                ];
                
                header('Location: install.php?step=3');
                exit;
                
            } catch (Exception $e) {
                $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // إعداد الموقع والمدير
            $site_name = $_POST['site_name'];
            $site_url = $_POST['site_url'];
            $site_email = $_POST['site_email'];
            $admin_email = $_POST['admin_email'];
            $admin_password = $_POST['admin_password'];
            
            // إنشاء ملف التكوين
            $config_content = "<?php\n";
            $config_content .= "define('SITE_NAME', '$site_name');\n";
            $config_content .= "define('SITE_URL', '$site_url');\n";
            $config_content .= "define('SITE_EMAIL', '$site_email');\n";
            $config_content .= "// باقي الإعدادات...\n";
            
            file_put_contents('config/config.php', $config_content);
            
            // تحديث بيانات المدير
            $db_config = $_SESSION['db_config'];
            $pdo = new PDO("mysql:host={$db_config['host']};dbname={$db_config['name']}", 
                          $db_config['user'], $db_config['pass']);
            
            $password_hash = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET email = ?, password_hash = ? WHERE user_type = 'admin'");
            $stmt->execute([$admin_email, $password_hash]);
            
            header('Location: install.php?step=4');
            exit;
            break;
    }
}

session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نقرة هوست</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .install-card { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .install-header { background: linear-gradient(135deg, #2563eb, #3b82f6); color: white; padding: 2rem; border-radius: 10px 10px 0 0; text-align: center; }
        .install-body { padding: 2rem; }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
        .step { width: 40px; height: 40px; border-radius: 50%; background: #e5e7eb; color: #6b7280; display: flex; align-items: center; justify-content: center; margin: 0 10px; font-weight: bold; }
        .step.active { background: #2563eb; color: white; }
        .step.completed { background: #10b981; color: white; }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h1>تثبيت نقرة هوست</h1>
                <p>معالج التثبيت السريع</p>
            </div>
            
            <div class="install-body">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                    <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">4</div>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <h3>التحقق من متطلبات النظام</h3>
                    <div class="requirements">
                        <div class="requirement">
                            <span>إصدار PHP:</span>
                            <span class="badge <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo PHP_VERSION; ?>
                            </span>
                        </div>
                        <div class="requirement">
                            <span>PDO MySQL:</span>
                            <span class="badge <?php echo extension_loaded('pdo_mysql') ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                        <div class="requirement">
                            <span>mbstring:</span>
                            <span class="badge <?php echo extension_loaded('mbstring') ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo extension_loaded('mbstring') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                    </div>
                    <form method="POST">
                        <button type="submit" class="btn btn-primary w-100 mt-3">التالي</button>
                    </form>
                    
                <?php elseif ($step == 2): ?>
                    <h3>إعداد قاعدة البيانات</h3>
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">خادم قاعدة البيانات</label>
                            <input type="text" name="db_host" class="form-control" value="localhost" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم قاعدة البيانات</label>
                            <input type="text" name="db_name" class="form-control" value="nakra_host" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" name="db_user" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" name="db_pass" class="form-control">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">التالي</button>
                    </form>
                    
                <?php elseif ($step == 3): ?>
                    <h3>إعداد الموقع والمدير</h3>
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">اسم الموقع</label>
                            <input type="text" name="site_name" class="form-control" value="نقرة هوست" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رابط الموقع</label>
                            <input type="url" name="site_url" class="form-control" value="https://nakra.host" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">بريد الموقع</label>
                            <input type="email" name="site_email" class="form-control" value="<EMAIL>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">بريد المدير</label>
                            <input type="email" name="admin_email" class="form-control" value="<EMAIL>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة مرور المدير</label>
                            <input type="password" name="admin_password" class="form-control" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">إنهاء التثبيت</button>
                    </form>
                    
                <?php elseif ($step == 4): ?>
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        <h3>تم التثبيت بنجاح!</h3>
                        <p>تم تثبيت نقرة هوست بنجاح. يمكنك الآن البدء في استخدام الموقع.</p>
                        <div class="d-grid gap-2">
                            <a href="index.php" class="btn btn-primary">الذهاب للموقع</a>
                            <a href="admin/" class="btn btn-outline-primary">لوحة تحكم الإدارة</a>
                        </div>
                        <div class="alert alert-warning mt-3">
                            <strong>مهم:</strong> احذف ملف install.php من الخادم لأسباب أمنية.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
