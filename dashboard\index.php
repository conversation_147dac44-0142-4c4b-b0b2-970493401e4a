<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من تسجيل الدخول
if (!$auth->isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$user = $auth->getCurrentUser();
$pageTitle = 'لوحة التحكم';

// الحصول على إحصائيات المستخدم
$database = new Database();
$db = $database->getConnection();

// عدد الخدمات النشطة
$stmt = $db->prepare("SELECT COUNT(*) as active_services FROM orders WHERE user_id = :user_id AND order_status = 'active'");
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$activeServices = $stmt->fetch()['active_services'];

// عدد الفواتير غير المدفوعة
$stmt = $db->prepare("SELECT COUNT(*) as unpaid_invoices FROM invoices WHERE user_id = :user_id AND status = 'unpaid'");
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$unpaidInvoices = $stmt->fetch()['unpaid_invoices'];

// عدد التذاكر المفتوحة
$stmt = $db->prepare("SELECT COUNT(*) as open_tickets FROM support_tickets WHERE user_id = :user_id AND status IN ('open', 'in_progress')");
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$openTickets = $stmt->fetch()['open_tickets'];

// آخر الفواتير
$stmt = $db->prepare("SELECT * FROM invoices WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$recentInvoices = $stmt->fetchAll();

// آخر التذاكر
$stmt = $db->prepare("SELECT * FROM support_tickets WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$recentTickets = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="dashboard-content">
    <div class="container-fluid">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="welcome-title">مرحباً، <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
                    <p class="welcome-subtitle">إليك نظرة سريعة على حسابك وخدماتك</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="welcome-actions">
                        <a href="services/new-order.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            طلب خدمة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $activeServices; ?></h3>
                        <p>الخدمات النشطة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $unpaidInvoices; ?></h3>
                        <p>فواتير معلقة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $openTickets; ?></h3>
                        <p>تذاكر الدعم</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3>99.9%</h3>
                        <p>وقت التشغيل</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Invoices -->
            <div class="col-lg-6 mb-4">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-file-invoice-dollar"></i>
                            آخر الفواتير
                        </h5>
                        <a href="billing/invoices.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentInvoices)): ?>
                            <div class="empty-state">
                                <i class="fas fa-file-invoice"></i>
                                <p>لا توجد فواتير حتى الآن</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentInvoices as $invoice): ?>
                                            <tr>
                                                <td>
                                                    <a href="billing/invoice.php?id=<?php echo $invoice['id']; ?>">
                                                        <?php echo $invoice['invoice_number']; ?>
                                                    </a>
                                                </td>
                                                <td><?php echo number_format($invoice['total_amount'], 2); ?> جنيه</td>
                                                <td>
                                                    <span class="badge badge-<?php echo $invoice['status'] === 'paid' ? 'success' : 'warning'; ?>">
                                                        <?php echo $invoice['status'] === 'paid' ? 'مدفوعة' : 'معلقة'; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y/m/d', strtotime($invoice['created_at'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Support Tickets -->
            <div class="col-lg-6 mb-4">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-headset"></i>
                            تذاكر الدعم
                        </h5>
                        <a href="support/tickets.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentTickets)): ?>
                            <div class="empty-state">
                                <i class="fas fa-ticket-alt"></i>
                                <p>لا توجد تذاكر دعم</p>
                                <a href="support/new-ticket.php" class="btn btn-primary btn-sm">إنشاء تذكرة جديدة</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم التذكرة</th>
                                            <th>الموضوع</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentTickets as $ticket): ?>
                                            <tr>
                                                <td>
                                                    <a href="support/ticket.php?id=<?php echo $ticket['id']; ?>">
                                                        <?php echo $ticket['ticket_number']; ?>
                                                    </a>
                                                </td>
                                                <td><?php echo htmlspecialchars(substr($ticket['subject'], 0, 30)) . '...'; ?></td>
                                                <td>
                                                    <span class="badge badge-<?php 
                                                        echo $ticket['status'] === 'open' ? 'primary' : 
                                                            ($ticket['status'] === 'closed' ? 'secondary' : 'warning'); 
                                                    ?>">
                                                        <?php 
                                                            $statusLabels = [
                                                                'open' => 'مفتوحة',
                                                                'in_progress' => 'قيد المعالجة',
                                                                'waiting_reply' => 'في انتظار الرد',
                                                                'closed' => 'مغلقة'
                                                            ];
                                                            echo $statusLabels[$ticket['status']];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y/m/d', strtotime($ticket['created_at'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-bolt"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="services/new-order.php" class="quick-action-card">
                                    <div class="quick-action-icon bg-primary">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                    <h6>طلب خدمة جديدة</h6>
                                    <p>اطلب استضافة أو خدمة جديدة</p>
                                </a>
                            </div>
                            
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="billing/invoices.php" class="quick-action-card">
                                    <div class="quick-action-icon bg-success">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    <h6>دفع الفواتير</h6>
                                    <p>ادفع فواتيرك المعلقة</p>
                                </a>
                            </div>
                            
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="support/new-ticket.php" class="quick-action-card">
                                    <div class="quick-action-icon bg-info">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <h6>طلب دعم فني</h6>
                                    <p>احصل على مساعدة من فريقنا</p>
                                </a>
                            </div>
                            
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="account/profile.php" class="quick-action-card">
                                    <div class="quick-action-icon bg-warning">
                                        <i class="fas fa-user-cog"></i>
                                    </div>
                                    <h6>إعدادات الحساب</h6>
                                    <p>تحديث بياناتك الشخصية</p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
