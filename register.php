<?php
require_once 'config/config.php';
require_once 'includes/auth.php';

$auth = new Auth();

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if ($auth->isLoggedIn()) {
    header('Location: dashboard/');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $firstName = trim($_POST['first_name']);
    $lastName = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    $agreeTerms = isset($_POST['agree_terms']);
    
    // التحقق من البيانات
    if (empty($firstName) || empty($lastName) || empty($email) || empty($password)) {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    } elseif ($password !== $confirmPassword) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (strlen($password) < 8) {
        $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    } elseif (!$agreeTerms) {
        $error = 'يجب الموافقة على الشروط والأحكام';
    } else {
        $result = $auth->register($firstName, $lastName, $email, $phone, $password);
        
        if ($result['success']) {
            $success = $result['message'];
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="auth-card">
                        <div class="auth-header">
                            <div class="auth-logo">
                                <img src="assets/images/logo.png" alt="<?php echo SITE_NAME; ?>">
                                <h2><?php echo SITE_NAME; ?></h2>
                            </div>
                            <h3>إنشاء حساب جديد</h3>
                            <p>انضم إلينا واستمتع بأفضل خدمات الاستضافة</p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <?php echo $success; ?>
                                <div class="mt-3">
                                    <a href="login.php" class="btn btn-success">تسجيل الدخول</a>
                                </div>
                            </div>
                        <?php else: ?>
                        
                        <form method="POST" class="auth-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="first_name">الاسم الأول</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-user"></i>
                                            </span>
                                            <input type="text" 
                                                   id="first_name" 
                                                   name="first_name" 
                                                   class="form-control" 
                                                   placeholder="الاسم الأول"
                                                   value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>"
                                                   required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="last_name">الاسم الأخير</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-user"></i>
                                            </span>
                                            <input type="text" 
                                                   id="last_name" 
                                                   name="last_name" 
                                                   class="form-control" 
                                                   placeholder="الاسم الأخير"
                                                   value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>"
                                                   required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" 
                                           id="email" 
                                           name="email" 
                                           class="form-control" 
                                           placeholder="أدخل بريدك الإلكتروني"
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                           required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">رقم الهاتف (اختياري)</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                    <input type="tel" 
                                           id="phone" 
                                           name="phone" 
                                           class="form-control" 
                                           placeholder="رقم الهاتف"
                                           value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="password">كلمة المرور</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-lock"></i>
                                            </span>
                                            <input type="password" 
                                                   id="password" 
                                                   name="password" 
                                                   class="form-control" 
                                                   placeholder="كلمة المرور"
                                                   required>
                                            <button type="button" class="btn btn-outline-secondary toggle-password" data-target="password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">
                                            يجب أن تكون 8 أحرف على الأقل
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="confirm_password">تأكيد كلمة المرور</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-lock"></i>
                                            </span>
                                            <input type="password" 
                                                   id="confirm_password" 
                                                   name="confirm_password" 
                                                   class="form-control" 
                                                   placeholder="تأكيد كلمة المرور"
                                                   required>
                                            <button type="button" class="btn btn-outline-secondary toggle-password" data-target="confirm_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" 
                                           id="agree_terms" 
                                           name="agree_terms" 
                                           class="form-check-input"
                                           required>
                                    <label for="agree_terms" class="form-check-label">
                                        أوافق على <a href="terms.php" target="_blank">الشروط والأحكام</a> و <a href="privacy.php" target="_blank">سياسة الخصوصية</a>
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-plus"></i>
                                إنشاء الحساب
                            </button>
                        </form>
                        
                        <?php endif; ?>
                        
                        <div class="auth-footer">
                            <p>لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
                            <div class="auth-links">
                                <a href="index.php">
                                    <i class="fas fa-home"></i>
                                    العودة للرئيسية
                                </a>
                                <a href="#contact">
                                    <i class="fas fa-headset"></i>
                                    الدعم الفني
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // تبديل إظهار/إخفاء كلمة المرور
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const passwordInput = document.querySelector('#' + targetId);
                const icon = this.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // التحقق من تطابق كلمات المرور
        document.querySelector('#confirm_password').addEventListener('input', function() {
            const password = document.querySelector('#password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // التحقق من قوة كلمة المرور
        document.querySelector('#password').addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = document.querySelector('#password-strength');
            
            if (password.length < 8) {
                this.setCustomValidity('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
