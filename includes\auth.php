<?php
/**
 * نظام المصادقة والأمان - نقرة هوست
 * Authentication and Security System - Nakra Host
 */

require_once '../config/config.php';

class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($firstName, $lastName, $email, $phone, $password) {
        try {
            // التحقق من وجود البريد الإلكتروني
            if ($this->emailExists($email)) {
                return ['success' => false, 'message' => 'البريد الإلكتروني مستخدم بالفعل'];
            }
            
            // تشفير كلمة المرور
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            
            // إنشاء رمز التحقق
            $verificationToken = bin2hex(random_bytes(32));
            
            // إدراج المستخدم الجديد
            $query = "INSERT INTO users (first_name, last_name, email, phone, password_hash, verification_token) 
                     VALUES (:first_name, :last_name, :email, :phone, :password_hash, :verification_token)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':first_name', $firstName);
            $stmt->bindParam(':last_name', $lastName);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':password_hash', $passwordHash);
            $stmt->bindParam(':verification_token', $verificationToken);
            
            if ($stmt->execute()) {
                $userId = $this->db->lastInsertId();
                
                // إرسال بريد التحقق
                $this->sendVerificationEmail($email, $verificationToken);
                
                // تسجيل النشاط
                $this->logActivity($userId, 'register', 'تم إنشاء حساب جديد');
                
                return [
                    'success' => true, 
                    'message' => 'تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني',
                    'user_id' => $userId
                ];
            }
            
            return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الحساب'];
            
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في النظام'];
        }
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($email, $password, $rememberMe = false) {
        try {
            $query = "SELECT id, first_name, last_name, email, password_hash, user_type, status, email_verified 
                     FROM users WHERE email = :email";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if (!$user) {
                return ['success' => false, 'message' => 'البريد الإلكتروني غير موجود'];
            }
            
            if (!password_verify($password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'كلمة المرور غير صحيحة'];
            }
            
            if ($user['status'] !== 'active') {
                return ['success' => false, 'message' => 'الحساب غير مفعل'];
            }
            
            // إنشاء الجلسة
            $this->createSession($user, $rememberMe);
            
            // تسجيل النشاط
            $this->logActivity($user['id'], 'login', 'تسجيل دخول ناجح');
            
            return [
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['first_name'] . ' ' . $user['last_name'],
                    'email' => $user['email'],
                    'type' => $user['user_type']
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في النظام'];
        }
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'تسجيل خروج');
        }
        
        // مسح الجلسة
        session_destroy();
        
        // مسح الكوكيز
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
        
        return ['success' => true, 'message' => 'تم تسجيل الخروج بنجاح'];
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        if (isset($_SESSION['user_id']) && isset($_SESSION['user_email'])) {
            return true;
        }
        
        // التحقق من كوكيز التذكر
        if (isset($_COOKIE['remember_token'])) {
            return $this->validateRememberToken($_COOKIE['remember_token']);
        }
        
        return false;
    }
    
    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        $query = "SELECT id, first_name, last_name, email, user_type, status 
                 FROM users WHERE id = :user_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    /**
     * التحقق من وجود البريد الإلكتروني
     */
    private function emailExists($email) {
        $query = "SELECT id FROM users WHERE email = :email";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * إنشاء جلسة المستخدم
     */
    private function createSession($user, $rememberMe = false) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['login_time'] = time();
        
        // إنشاء رمز التذكر إذا طُلب ذلك
        if ($rememberMe) {
            $rememberToken = bin2hex(random_bytes(32));
            setcookie('remember_token', $rememberToken, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
            
            // حفظ الرمز في قاعدة البيانات
            $this->saveRememberToken($user['id'], $rememberToken);
        }
    }
    
    /**
     * حفظ رمز التذكر
     */
    private function saveRememberToken($userId, $token) {
        $hashedToken = hash('sha256', $token);
        $expiresAt = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60));
        
        $query = "INSERT INTO user_tokens (user_id, token_hash, expires_at, type) 
                 VALUES (:user_id, :token_hash, :expires_at, 'remember')
                 ON DUPLICATE KEY UPDATE token_hash = :token_hash, expires_at = :expires_at";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':token_hash', $hashedToken);
        $stmt->bindParam(':expires_at', $expiresAt);
        $stmt->execute();
    }
    
    /**
     * التحقق من رمز التذكر
     */
    private function validateRememberToken($token) {
        $hashedToken = hash('sha256', $token);
        
        $query = "SELECT u.id, u.first_name, u.last_name, u.email, u.user_type 
                 FROM users u 
                 JOIN user_tokens t ON u.id = t.user_id 
                 WHERE t.token_hash = :token_hash 
                 AND t.expires_at > NOW() 
                 AND t.type = 'remember'
                 AND u.status = 'active'";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':token_hash', $hashedToken);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if ($user) {
            $this->createSession($user);
            return true;
        }
        
        return false;
    }
    
    /**
     * إرسال بريد التحقق
     */
    private function sendVerificationEmail($email, $token) {
        $verificationUrl = SITE_URL . "/verify.php?token=" . $token;
        
        $subject = "تفعيل حساب " . SITE_NAME;
        $message = "
        <html>
        <body style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
            <h2>مرحباً بك في " . SITE_NAME . "</h2>
            <p>شكراً لك على التسجيل في موقعنا. يرجى النقر على الرابط التالي لتفعيل حسابك:</p>
            <p><a href='{$verificationUrl}' style='background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تفعيل الحساب</a></p>
            <p>إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد.</p>
            <hr>
            <p>فريق " . SITE_NAME . "</p>
        </body>
        </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . SITE_EMAIL . "\r\n";
        
        mail($email, $subject, $message, $headers);
    }
    
    /**
     * تسجيل النشاط
     */
    private function logActivity($userId, $action, $description) {
        $query = "INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
                 VALUES (:user_id, :action, :description, :ip_address, :user_agent)";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
        $stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT']);
        $stmt->execute();
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public function hasPermission($permission) {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            return false;
        }
        
        // المدير له جميع الصلاحيات
        if ($user['user_type'] === 'admin') {
            return true;
        }
        
        // يمكن إضافة منطق صلاحيات أكثر تعقيداً هنا
        return false;
    }
}
?>
