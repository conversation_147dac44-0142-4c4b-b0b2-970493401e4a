# نقرة هوست - إعدادات Apache
# Nakra Host - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS (في بيئة الإنتاج)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www إلى non-www (اختياري)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# حماية الملفات الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

# حماية مجلدات النظام
<IfModule mod_rewrite.c>
    RewriteRule ^config/ - [F,L]
    RewriteRule ^includes/ - [F,L]
    RewriteRule ^database/ - [F,L]
</IfModule>

# منع الوصول للملفات المخفية
<Files ".*">
    Order Allow,Deny
    Deny from all
</Files>

# السماح بالوصول لـ .well-known (لشهادات SSL)
<Files ".well-known">
    Order Allow,Deny
    Allow from all
</Files>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    # ضغط النصوص
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
    
    # ضغط الخطوط
    AddOutputFilterByType DEFLATE font/truetype
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE application/font-woff
    AddOutputFilterByType DEFLATE application/font-woff2
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    
    # ضغط الصور SVG
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # الخطوط
    ExpiresByType font/truetype "access plus 1 year"
    ExpiresByType font/opentype "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # ملفات أخرى
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
</IfModule>

# إعدادات Cache-Control
<IfModule mod_headers.c>
    # إزالة ETag
    Header unset ETag
    FileETag None
    
    # إعدادات Cache للملفات الثابتة
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|eot)$">
        Header set Cache-Control "max-age=31536000, public, immutable"
    </FilesMatch>
    
    # إعدادات Cache للـ HTML
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "max-age=3600, public"
    </FilesMatch>
    
    # إعدادات الأمان
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # Content Security Policy (يمكن تخصيصها حسب الحاجة)
    # Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self'"
</IfModule>

# منع hotlinking للصور
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?nakra\.host [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?nakraformarketing\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [F]
</IfModule>

# تحسين الأداء - إزالة الإصدارات من الاستعلامات
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} ^v=(.*)$
    RewriteRule ^(.*)$ /$1? [R=301,L]
</IfModule>

# صفحات الخطأ المخصصة
ErrorDocument 400 /error-pages/400.html
ErrorDocument 401 /error-pages/401.html
ErrorDocument 403 /error-pages/403.html
ErrorDocument 404 /error-pages/404.html
ErrorDocument 500 /error-pages/500.html
ErrorDocument 502 /error-pages/502.html
ErrorDocument 503 /error-pages/503.html

# تحسين الأمان - منع عرض محتويات المجلدات
Options -Indexes

# تحسين الأمان - منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp|temp|log)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تحسين الأمان - منع الوصول لملفات المطورين
<FilesMatch "\.(git|gitignore|gitattributes|env|composer\.json|composer\.lock|package\.json|package-lock\.json|yarn\.lock)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تحسين الأداء - ضغط إضافي
<IfModule mod_deflate.c>
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|ico)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# إعدادات PHP (إذا كان مسموحاً)
<IfModule mod_php7.c>
    php_value upload_max_filesize 64M
    php_value post_max_size 64M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
    php_value session.gc_maxlifetime 3600
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log /var/log/php_errors.log
</IfModule>

# تحسين الأداء للملفات الكبيرة
<IfModule mod_fcgid.c>
    FcgidMaxRequestLen 67108864
</IfModule>

# منع الوصول المباشر لملفات PHP في مجلدات معينة
<IfModule mod_rewrite.c>
    RewriteRule ^(uploads|assets)/.*\.php$ - [F,L]
</IfModule>

# تحسين الأمان - منع SQL injection في URLs
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# تحسين الأداء - تفعيل Keep-Alive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# إعدادات MIME types إضافية
<IfModule mod_mime.c>
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType image/webp .webp
    AddType application/json .json
    AddType application/ld+json .jsonld
</IfModule>
