<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';

$auth = new Auth();

// التحقق من تسجيل الدخول
if (!$auth->isLoggedIn()) {
    header('Location: ../../login.php');
    exit;
}

$user = $auth->getCurrentUser();
$pageTitle = 'تذاكر الدعم';

// الحصول على التذاكر
$database = new Database();
$db = $database->getConnection();

// فلترة التذاكر
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$priority_filter = isset($_GET['priority']) ? $_GET['priority'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

$where_clause = "WHERE user_id = :user_id";
$params = [':user_id' => $user['id']];

if ($status_filter) {
    $where_clause .= " AND status = :status";
    $params[':status'] = $status_filter;
}

if ($priority_filter) {
    $where_clause .= " AND priority = :priority";
    $params[':priority'] = $priority_filter;
}

// عدد التذاكر الإجمالي
$count_query = "SELECT COUNT(*) as total FROM support_tickets $where_clause";
$count_stmt = $db->prepare($count_query);
$count_stmt->execute($params);
$total_tickets = $count_stmt->fetch()['total'];
$total_pages = ceil($total_tickets / $limit);

// جلب التذاكر
$query = "SELECT st.*, 
                 (SELECT COUNT(*) FROM ticket_messages tm WHERE tm.ticket_id = st.id) as message_count,
                 (SELECT created_at FROM ticket_messages tm WHERE tm.ticket_id = st.id ORDER BY created_at DESC LIMIT 1) as last_reply
          FROM support_tickets st 
          $where_clause 
          ORDER BY st.created_at DESC 
          LIMIT :limit OFFSET :offset";

$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$tickets = $stmt->fetchAll();

// إحصائيات التذاكر
$stats_query = "SELECT 
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN status = 'open' THEN 1 END) as open_count,
                    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
                    COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_count
                FROM support_tickets WHERE user_id = :user_id";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->bindParam(':user_id', $user['id']);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

include '../includes/header.php';
?>

<div class="dashboard-content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-headset"></i>
                        تذاكر الدعم الفني
                    </h1>
                    <p class="page-subtitle">إدارة ومتابعة طلبات الدعم الفني</p>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="new-ticket.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        تذكرة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['total_count']; ?></h3>
                        <p>إجمالي التذاكر</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['open_count']; ?></h3>
                        <p>تذاكر مفتوحة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['in_progress_count']; ?></h3>
                        <p>قيد المعالجة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['closed_count']; ?></h3>
                        <p>تذاكر مغلقة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="dashboard-card mb-4">
            <div class="card-body">
                <form method="GET" class="row align-items-end">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <label for="status" class="form-label">حالة التذكرة</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>مفتوحة</option>
                            <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>قيد المعالجة</option>
                            <option value="waiting_reply" <?php echo $status_filter === 'waiting_reply' ? 'selected' : ''; ?>>في انتظار الرد</option>
                            <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>مغلقة</option>
                        </select>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <label for="priority" class="form-label">الأولوية</label>
                        <select name="priority" id="priority" class="form-select">
                            <option value="">جميع الأولويات</option>
                            <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>منخفضة</option>
                            <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                            <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>عالية</option>
                            <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>عاجلة</option>
                        </select>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <a href="tickets.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tickets Table -->
        <div class="dashboard-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-list"></i>
                    قائمة التذاكر
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($tickets)): ?>
                    <div class="empty-state">
                        <i class="fas fa-ticket-alt"></i>
                        <h5>لا توجد تذاكر دعم</h5>
                        <p>لم يتم العثور على أي تذاكر بالمعايير المحددة</p>
                        <a href="new-ticket.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إنشاء تذكرة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم التذكرة</th>
                                    <th>الموضوع</th>
                                    <th>القسم</th>
                                    <th>الأولوية</th>
                                    <th>الحالة</th>
                                    <th>الردود</th>
                                    <th>آخر رد</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tickets as $ticket): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($ticket['ticket_number']); ?></strong>
                                        </td>
                                        <td>
                                            <a href="ticket.php?id=<?php echo $ticket['id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars(substr($ticket['subject'], 0, 50)) . (strlen($ticket['subject']) > 50 ? '...' : ''); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php
                                                $departments = [
                                                    'technical' => 'دعم فني',
                                                    'billing' => 'الفواتير',
                                                    'sales' => 'المبيعات',
                                                    'general' => 'عام'
                                                ];
                                                echo $departments[$ticket['department']];
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                                $priority_classes = [
                                                    'low' => 'secondary',
                                                    'medium' => 'info',
                                                    'high' => 'warning',
                                                    'urgent' => 'danger'
                                                ];
                                                $priority_labels = [
                                                    'low' => 'منخفضة',
                                                    'medium' => 'متوسطة',
                                                    'high' => 'عالية',
                                                    'urgent' => 'عاجلة'
                                                ];
                                            ?>
                                            <span class="badge badge-<?php echo $priority_classes[$ticket['priority']]; ?>">
                                                <?php echo $priority_labels[$ticket['priority']]; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                                $status_classes = [
                                                    'open' => 'primary',
                                                    'in_progress' => 'warning',
                                                    'waiting_reply' => 'info',
                                                    'closed' => 'secondary'
                                                ];
                                                $status_labels = [
                                                    'open' => 'مفتوحة',
                                                    'in_progress' => 'قيد المعالجة',
                                                    'waiting_reply' => 'في انتظار الرد',
                                                    'closed' => 'مغلقة'
                                                ];
                                            ?>
                                            <span class="badge badge-<?php echo $status_classes[$ticket['status']]; ?>">
                                                <?php echo $status_labels[$ticket['status']]; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-light">
                                                <?php echo $ticket['message_count']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                                if ($ticket['last_reply']) {
                                                    $last_reply = new DateTime($ticket['last_reply']);
                                                    echo $last_reply->format('Y/m/d H:i');
                                                } else {
                                                    echo '-';
                                                }
                                            ?>
                                        </td>
                                        <td>
                                            <?php 
                                                $created = new DateTime($ticket['created_at']);
                                                echo $created->format('Y/m/d H:i');
                                            ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="ticket.php?id=<?php echo $ticket['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="عرض التذكرة">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if ($ticket['status'] !== 'closed'): ?>
                                                    <a href="ticket.php?id=<?php echo $ticket['id']; ?>#reply" 
                                                       class="btn btn-sm btn-outline-success" 
                                                       title="الرد">
                                                        <i class="fas fa-reply"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تنقل الصفحات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&priority=<?php echo $priority_filter; ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&priority=<?php echo $priority_filter; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&priority=<?php echo $priority_filter; ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
