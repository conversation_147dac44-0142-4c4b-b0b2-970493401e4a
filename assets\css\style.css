/* نقرة هوست - ملف الأنماط الرئيسي */

:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --gray-color: #6b7280;
    --white-color: #ffffff;
    
    --font-family: 'Cairo', sans-serif;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white-color);
    direction: rtl;
    text-align: right;
}

/* Header Styles */
.header {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header.scrolled {
    background: rgba(30, 41, 59, 0.98);
    box-shadow: 0 2px 30px rgba(0, 0, 0, 0.2);
}

.navbar {
    padding: 1rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--white-color) !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.brand-logo {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 1.2rem;
    color: white;
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
}

.brand-text {
    background: linear-gradient(45deg, #fff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
}

.navbar-nav .nav-link {
    color: var(--white-color) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.dropdown-menu {
    background-color: var(--white-color);
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    margin-top: 0.5rem;
}

.dropdown-item {
    color: var(--dark-color);
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white-color);
    padding: 0;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    opacity: 0.9;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="particles" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="5" cy="15" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23particles)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 2rem 0;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-badge i {
    color: #ffd700;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.gradient-text {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-title .text-primary {
    color: #60a5fa !important;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
    max-width: 90%;
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 1.2rem;
}

.feature-content h6 {
    margin: 0;
    font-weight: 700;
    font-size: 1rem;
}

.feature-content span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-item h4 {
    font-size: 2rem;
    font-weight: 900;
    margin: 0;
    color: #ffd700;
}

.stat-item span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-image {
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-image-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 500px;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #333;
    font-weight: 600;
    animation: floatCard 6s ease-in-out infinite;
}

.floating-card i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.card-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    left: 5%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20%;
    right: 15%;
    animation-delay: 4s;
}

.main-server-image {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: pulse 3s ease-in-out infinite;
}

@keyframes floatCard {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Features Section */
.features-section {
    background-color: var(--light-color);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--gray-color);
    margin-bottom: 3rem;
}

.feature-card {
    background: var(--white-color);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white-color);
    font-size: 2rem;
}

.feature-card h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-card p {
    color: var(--gray-color);
    line-height: 1.7;
}

/* Contact Info */
.contact-info {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 2rem 0;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-weight: 500;
}

.contact-item i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

/* Buttons */
.btn {
    font-family: var(--font-family);
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white-color);
}

.btn-outline-light {
    border: 2px solid var(--white-color);
    color: var(--white-color);
    background: transparent;
}

.btn-outline-light:hover {
    background: var(--white-color);
    color: var(--primary-color);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

/* Utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-light {
    background-color: var(--light-color) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Hosting Plans Section */
.hosting-plans-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.plans-tabs .nav-pills {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    padding: 0.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.plans-tabs .nav-link {
    border-radius: 25px;
    padding: 1rem 2rem;
    font-weight: 600;
    color: var(--gray-color);
    border: none;
    background: transparent;
    transition: all 0.3s ease;
}

.plans-tabs .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.4);
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    border: 2px solid transparent;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(37, 99, 235, 0.2);
}

.pricing-card.featured::before {
    height: 6px;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
}

.popular-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.5rem 1.5rem;
    border-radius: 0 0 15px 15px;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.pricing-header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

.pricing-header h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.price .currency {
    font-size: 1rem;
    color: var(--gray-color);
    font-weight: 500;
}

.price .amount {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
}

.price .period {
    font-size: 1rem;
    color: var(--gray-color);
    font-weight: 500;
}

.discount-badge {
    display: inline-block;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.pricing-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
    font-weight: 500;
    color: var(--dark-color);
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features li i {
    color: var(--success-color);
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.pricing-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #f1f5f9;
}

.pricing-footer .btn {
    font-weight: 700;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.pricing-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
}

/* Offers Section */
.offers-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.offers-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="offers-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23offers-pattern)"/></svg>');
}

.offers-section .section-title,
.offers-section .section-subtitle {
    color: white;
}

.offer-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.offer-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.offer-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0 20px 0 20px;
    font-weight: 700;
    font-size: 0.9rem;
}

.offer-content {
    text-align: center;
    color: #333;
}

.offer-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.offer-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.offer-content p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.offer-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.offer-code {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.9rem;
}

.offer-validity {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

/* Why Choose Section */
.why-choose-section {
    background: white;
}

.why-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.why-feature {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.why-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.why-content h5 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.why-content p {
    color: var(--gray-color);
    margin: 0;
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.stat-box {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(37, 99, 235, 0.3);
}

.stat-box h3 {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.stat-box p {
    margin: 0;
    font-weight: 600;
    opacity: 0.9;
}
