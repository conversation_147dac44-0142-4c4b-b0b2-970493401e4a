/* نقرة هوست - ملف الأنماط الرئيسي */

:root {
    /* نظام الألوان الجديد المتناسق */
    --primary-color: #0f172a;
    --secondary-color: #1e293b;
    --accent-color: #3b82f6;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0284c7;

    /* ألوان الخلفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #0f172a;
    --bg-light: #f1f5f9;

    /* ألوان النصوص */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;

    /* ألو<PERSON> الحدود */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #334155;

    /* متدرجات لونية */
    --gradient-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --gradient-accent: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
    --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);

    /* إعدادات التصميم */
    --font-family: 'Cairo', sans-serif;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white-color);
    direction: rtl;
    text-align: right;
}

/* Header Styles */
.header {
    background: var(--bg-primary);
    box-shadow: 0 2px 20px rgba(15, 23, 42, 0.08);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-light);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 30px rgba(15, 23, 42, 0.12);
}

.navbar {
    padding: 1rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 800;
    font-size: 1.75rem;
    color: var(--text-primary) !important;
    text-decoration: none;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: translateY(-2px);
    color: var(--accent-color) !important;
}

.brand-logo {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    font-size: 1.5rem;
    color: var(--text-white);
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.brand-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.navbar-brand:hover .brand-logo::before {
    left: 100%;
}

.brand-text {
    color: var(--text-primary);
    font-weight: 800;
    letter-spacing: -0.5px;
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 600;
    margin: 0 0.25rem;
    padding: 0.75rem 1.25rem !important;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-accent);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--accent-color) !important;
    background-color: rgba(59, 130, 246, 0.05);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

.dropdown-menu {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius);
    margin-top: 0.5rem;
    padding: 0.5rem 0;
}

.dropdown-item {
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--accent-color);
    transform: translateX(5px);
}

.navbar-nav .btn {
    margin: 0 0.25rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    border: 2px solid transparent;
}

.btn-outline-light {
    color: var(--text-secondary);
    border-color: var(--border-color);
    background: transparent;
}

.btn-outline-light:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

.btn-primary {
    background: var(--gradient-accent);
    color: var(--text-white);
    border: none;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Hero Section */
.hero-section {
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 0;
    margin-top: 90px;
    position: relative;
    overflow: hidden;
    min-height: 90vh;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    opacity: 1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="particles" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(59,130,246,0.1)"/><circle cx="10" cy="30" r="0.5" fill="rgba(59,130,246,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23particles)"/></svg>');
    animation: float 25s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(1deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 2rem 0;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: var(--text-secondary);
    box-shadow: var(--box-shadow);
}

.hero-badge i {
    color: var(--warning-color);
    font-size: 1.1rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    color: var(--text-primary);
}

.gradient-text {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-title .text-primary {
    color: var(--accent-color) !important;
}

.hero-description {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    color: var(--text-secondary);
    line-height: 1.7;
    max-width: 95%;
    font-weight: 400;
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 1.2rem;
}

.feature-content h6 {
    margin: 0;
    font-weight: 700;
    font-size: 1rem;
}

.feature-content span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-item h4 {
    font-size: 2rem;
    font-weight: 900;
    margin: 0;
    color: #ffd700;
}

.stat-item span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-image {
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-image-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 500px;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #333;
    font-weight: 600;
    animation: floatCard 6s ease-in-out infinite;
}

.floating-card i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.card-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    left: 5%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20%;
    right: 15%;
    animation-delay: 4s;
}

.main-server-image {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: pulse 3s ease-in-out infinite;
}

@keyframes floatCard {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Features Section */
.features-section {
    background-color: var(--light-color);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--gray-color);
    margin-bottom: 3rem;
}

.feature-card {
    background: var(--white-color);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white-color);
    font-size: 2rem;
}

.feature-card h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-card p {
    color: var(--gray-color);
    line-height: 1.7;
}

/* Contact Info */
.contact-info {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 2rem 0;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-weight: 500;
}

.contact-item i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

/* Buttons */
.btn {
    font-family: var(--font-family);
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white-color);
}

.btn-outline-light {
    border: 2px solid var(--white-color);
    color: var(--white-color);
    background: transparent;
}

.btn-outline-light:hover {
    background: var(--white-color);
    color: var(--primary-color);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

/* Utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-light {
    background-color: var(--light-color) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Hosting Plans Section */
.hosting-plans-section {
    background: var(--bg-secondary);
    position: relative;
    padding: 5rem 0;
}

.plans-tabs .nav-pills {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 0.5rem;
    box-shadow: var(--box-shadow);
    display: inline-flex;
}

.plans-tabs .nav-link {
    border-radius: var(--border-radius);
    padding: 1rem 2rem;
    font-weight: 600;
    color: var(--text-secondary);
    border: none;
    background: transparent;
    transition: var(--transition);
    white-space: nowrap;
}

.plans-tabs .nav-link.active {
    background: var(--gradient-accent);
    color: var(--text-white);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.pricing-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    height: 100%;
    border: 1px solid var(--border-color);
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--accent-color);
}

.pricing-card.featured {
    border-color: var(--accent-color);
    transform: scale(1.02);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.15);
}

.pricing-card.featured::before {
    height: 4px;
    background: var(--gradient-success);
}

.popular-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-success);
    color: var(--text-white);
    padding: 0.6rem 1.5rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    font-weight: 700;
    font-size: 0.85rem;
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
    letter-spacing: 0.5px;
}

.pricing-header {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

.pricing-header h4 {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    letter-spacing: -0.5px;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.price .currency {
    font-size: 1.1rem;
    color: var(--text-muted);
    font-weight: 600;
}

.price .amount {
    font-size: 3.5rem;
    font-weight: 900;
    color: var(--accent-color);
    line-height: 1;
}

.price .period {
    font-size: 1.1rem;
    color: var(--text-muted);
    font-weight: 600;
}

.discount-badge {
    display: inline-block;
    background: var(--gradient-accent);
    color: var(--text-white);
    padding: 0.4rem 1rem;
    border-radius: var(--border-radius-lg);
    font-size: 0.85rem;
    font-weight: 700;
    box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
}

.pricing-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light);
    font-weight: 500;
    color: var(--text-primary);
    transition: var(--transition);
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features li:hover {
    color: var(--accent-color);
    transform: translateX(5px);
}

.pricing-features li i {
    color: var(--success-color);
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    background: rgba(5, 150, 105, 0.1);
    border-radius: 50%;
    padding: 4px;
}

.pricing-footer {
    margin-top: 2.5rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-light);
}

.pricing-footer .btn {
    font-weight: 700;
    padding: 1.2rem 2.5rem;
    border-radius: var(--border-radius);
    letter-spacing: 0.5px;
    transition: var(--transition);
    font-size: 1rem;
    text-transform: none;
    background: var(--gradient-accent);
    border: none;
    color: var(--text-white);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.pricing-footer .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Offers Section */
.offers-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.offers-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="offers-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23offers-pattern)"/></svg>');
}

.offers-section .section-title,
.offers-section .section-subtitle {
    color: white;
}

.offer-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.offer-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.offer-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0 20px 0 20px;
    font-weight: 700;
    font-size: 0.9rem;
}

.offer-content {
    text-align: center;
    color: #333;
}

.offer-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.offer-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.offer-content p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.offer-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.offer-code {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.9rem;
}

.offer-validity {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

/* Why Choose Section */
.why-choose-section {
    background: white;
}

.why-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.why-feature {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.why-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.why-content h5 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.why-content p {
    color: var(--gray-color);
    margin: 0;
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.stat-box {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(37, 99, 235, 0.3);
}

.stat-box h3 {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.stat-box p {
    margin: 0;
    font-weight: 600;
    opacity: 0.9;
}

/* Footer Styles */
.footer {
    background: var(--bg-dark);
    color: var(--text-white);
    margin-top: 5rem;
}

.footer-main {
    padding: 4rem 0 2rem;
}

.footer-section {
    height: 100%;
}

.footer-brand {
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.footer-logo .brand-logo {
    width: 45px;
    height: 45px;
    margin-left: 12px;
}

.footer-logo .brand-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-white);
}

.footer-description {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.footer-contact .contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: var(--text-light);
    transition: var(--transition);
}

.footer-contact .contact-item:hover {
    color: var(--accent-color);
    transform: translateX(5px);
}

.footer-contact .contact-item i {
    width: 20px;
    color: var(--accent-color);
}

.footer-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-white);
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-color);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 400;
}

.footer-links a:hover {
    color: var(--accent-color);
    transform: translateX(5px);
    display: inline-block;
}

.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright p {
    margin: 0;
    color: var(--text-light);
    font-size: 0.9rem;
}

.company-info {
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.company-info a {
    color: var(--accent-color);
    text-decoration: none;
}

.company-info a:hover {
    text-decoration: underline;
}

.footer-legal {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-legal a {
    color: var(--text-light);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-legal a:hover {
    color: var(--accent-color);
}

.footer-social {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: flex-end;
}

.footer-social span {
    color: var(--text-light);
    font-weight: 600;
}

.social-links {
    display: flex;
    gap: 0.75rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
    font-size: 1rem;
}

.social-link:hover {
    color: var(--text-white);
    transform: translateY(-3px);
}

.social-link.facebook:hover { background: #1877f2; }
.social-link.twitter:hover { background: #1da1f2; }
.social-link.linkedin:hover { background: #0077b5; }
.social-link.instagram:hover { background: #e4405f; }
.social-link.youtube:hover { background: #ff0000; }

.footer-trust {
    background: rgba(0, 0, 0, 0.5);
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.trust-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 600;
}

.trust-badge i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

/* Page Header Styles */
.page-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 8rem 0 4rem;
    margin-top: 90px;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--text-white);
}

/* Service Cards */
.service-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--accent-color);
}

.service-card.featured {
    border-color: var(--accent-color);
    transform: scale(1.02);
}

.service-card.featured::before {
    height: 4px;
    background: var(--gradient-success);
}

.service-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-success);
    color: var(--text-white);
    padding: 0.5rem 1.5rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    font-weight: 700;
    font-size: 0.85rem;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--text-white);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-align: center;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: var(--text-secondary);
}

.service-features li i {
    color: var(--success-color);
    font-size: 1rem;
}

.service-price {
    text-align: center;
    margin-bottom: 1.5rem;
}

.service-price .price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-color);
}

/* Domain Styles */
.domain-hero {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 8rem 0 4rem;
    margin-top: 90px;
}

.domain-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1rem;
}

.domain-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

.domain-search-form {
    max-width: 600px;
    margin: 0 auto;
}

.search-form .input-group {
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.search-form .form-control {
    border: none;
    padding: 1.2rem 1.5rem;
    font-size: 1.1rem;
    border-radius: 0;
}

.search-form .form-select {
    border: none;
    padding: 1.2rem 1rem;
    border-radius: 0;
    background: var(--bg-secondary);
}

.search-form .btn {
    padding: 1.2rem 2rem;
    font-weight: 700;
    border-radius: 0;
}

.search-suggestions {
    margin-top: 1.5rem;
    text-align: center;
}

.suggestion-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    cursor: pointer;
    transition: var(--transition);
}

.suggestion-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Domain Price Cards */
.domain-price-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--border-color);
    text-align: center;
    position: relative;
}

.domain-price-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.domain-price-card.featured {
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.domain-extension-name {
    font-size: 2rem;
    font-weight: 900;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.domain-price {
    margin-bottom: 1.5rem;
}

.domain-price .price {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--text-primary);
}

.domain-price .currency {
    font-size: 1rem;
    color: var(--text-muted);
    margin-right: 0.5rem;
}

.domain-features p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

/* Support Styles */
.support-hero {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 8rem 0 4rem;
    margin-top: 90px;
}

.support-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1rem;
}

.support-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.contact-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--border-color);
    text-align: center;
}

.contact-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-lg);
}

.contact-card.featured {
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--text-white);
}

.contact-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.contact-info {
    margin: 1.5rem 0;
}

.contact-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
}

.availability {
    display: inline-block;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
}

/* Contact Form */
.contact-form-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 3rem;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
}

/* CTA Section */
.cta-section {
    background: var(--gradient-accent);
    color: var(--text-white);
    text-align: center;
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}
