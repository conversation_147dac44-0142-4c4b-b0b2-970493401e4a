<?php
require_once 'config/config.php';
require_once 'includes/auth.php';

$auth = new Auth();

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if ($auth->isLoggedIn()) {
    header('Location: dashboard/');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $rememberMe = isset($_POST['remember_me']);
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
    } else {
        $result = $auth->login($email, $password, $rememberMe);
        
        if ($result['success']) {
            header('Location: dashboard/');
            exit;
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="auth-card">
                        <div class="auth-header">
                            <div class="auth-logo">
                                <img src="assets/images/logo.png" alt="<?php echo SITE_NAME; ?>">
                                <h2><?php echo SITE_NAME; ?></h2>
                            </div>
                            <h3>تسجيل الدخول</h3>
                            <p>أدخل بياناتك للوصول إلى حسابك</p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="auth-form">
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" 
                                           id="email" 
                                           name="email" 
                                           class="form-control" 
                                           placeholder="أدخل بريدك الإلكتروني"
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                           required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" 
                                           id="password" 
                                           name="password" 
                                           class="form-control" 
                                           placeholder="أدخل كلمة المرور"
                                           required>
                                    <button type="button" class="btn btn-outline-secondary toggle-password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check-group">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               id="remember_me" 
                                               name="remember_me" 
                                               class="form-check-input">
                                        <label for="remember_me" class="form-check-label">
                                            تذكرني
                                        </label>
                                    </div>
                                    <a href="forgot-password.php" class="forgot-link">
                                        نسيت كلمة المرور؟
                                    </a>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-sign-in-alt"></i>
                                تسجيل الدخول
                            </button>
                        </form>
                        
                        <div class="auth-footer">
                            <p>ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
                            <div class="auth-links">
                                <a href="index.php">
                                    <i class="fas fa-home"></i>
                                    العودة للرئيسية
                                </a>
                                <a href="#contact">
                                    <i class="fas fa-headset"></i>
                                    الدعم الفني
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // تبديل إظهار/إخفاء كلمة المرور
        document.querySelector('.toggle-password').addEventListener('click', function() {
            const passwordInput = document.querySelector('#password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // التحقق من صحة النموذج
        document.querySelector('.auth-form').addEventListener('submit', function(e) {
            const email = document.querySelector('#email').value.trim();
            const password = document.querySelector('#password').value;
            
            if (!email || !password) {
                e.preventDefault();
                alert('يرجى إدخال جميع البيانات المطلوبة');
                return;
            }
            
            // التحقق من صحة البريد الإلكتروني
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
        });
    </script>
</body>
</html>
