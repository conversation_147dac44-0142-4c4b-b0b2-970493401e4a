<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من تسجيل الدخول والصلاحيات
if (!$auth->isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

$user = $auth->getCurrentUser();

// التحقق من صلاحيات الإدارة
if ($user['user_type'] !== 'admin') {
    header('Location: ../dashboard/');
    exit;
}

$pageTitle = 'لوحة تحكم الإدارة';

// الحصول على الإحصائيات
$database = new Database();
$db = $database->getConnection();

// إحصائيات العملاء
$stmt = $db->query("SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_users,
    COUNT(CASE WHEN user_type = 'admin' THEN 1 END) as admin_users
    FROM users");
$user_stats = $stmt->fetch();

// إحصائيات الطلبات
$stmt = $db->query("SELECT 
    COUNT(*) as total_orders,
    COUNT(CASE WHEN order_status = 'active' THEN 1 END) as active_orders,
    COUNT(CASE WHEN order_status = 'pending' THEN 1 END) as pending_orders,
    SUM(amount) as total_revenue
    FROM orders");
$order_stats = $stmt->fetch();

// إحصائيات الفواتير
$stmt = $db->query("SELECT 
    COUNT(*) as total_invoices,
    COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
    COUNT(CASE WHEN status = 'unpaid' THEN 1 END) as unpaid_invoices,
    SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
    SUM(CASE WHEN status = 'unpaid' THEN total_amount ELSE 0 END) as unpaid_amount
    FROM invoices");
$invoice_stats = $stmt->fetch();

// إحصائيات التذاكر
$stmt = $db->query("SELECT 
    COUNT(*) as total_tickets,
    COUNT(CASE WHEN status = 'open' THEN 1 END) as open_tickets,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_tickets,
    COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_tickets
    FROM support_tickets");
$ticket_stats = $stmt->fetch();

// آخر الأنشطة
$stmt = $db->prepare("SELECT al.*, u.first_name, u.last_name 
                     FROM activity_logs al 
                     LEFT JOIN users u ON al.user_id = u.id 
                     ORDER BY al.created_at DESC 
                     LIMIT 10");
$stmt->execute();
$recent_activities = $stmt->fetchAll();

// آخر الطلبات
$stmt = $db->prepare("SELECT o.*, u.first_name, u.last_name, hp.name as plan_name 
                     FROM orders o 
                     JOIN users u ON o.user_id = u.id 
                     JOIN hosting_plans hp ON o.plan_id = hp.id 
                     ORDER BY o.created_at DESC 
                     LIMIT 5");
$stmt->execute();
$recent_orders = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="admin-content">
    <div class="container-fluid">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="welcome-title">مرحباً، <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
                    <p class="welcome-subtitle">لوحة تحكم الإدارة - نقرة هوست</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="welcome-actions">
                        <a href="users/add.php" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i>
                            إضافة عميل جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Overview -->
        <div class="row mb-4">
            <!-- Users Stats -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($user_stats['total_users']); ?></h3>
                        <p>إجمالي العملاء</p>
                        <small class="text-muted">
                            نشط: <?php echo $user_stats['active_users']; ?> | 
                            معلق: <?php echo $user_stats['pending_users']; ?>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Orders Stats -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($order_stats['total_orders']); ?></h3>
                        <p>إجمالي الطلبات</p>
                        <small class="text-muted">
                            نشط: <?php echo $order_stats['active_orders']; ?> | 
                            معلق: <?php echo $order_stats['pending_orders']; ?>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Revenue Stats -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($invoice_stats['paid_amount'], 0); ?> جنيه</h3>
                        <p>الإيرادات المحصلة</p>
                        <small class="text-muted">
                            معلق: <?php echo number_format($invoice_stats['unpaid_amount'], 0); ?> جنيه
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Support Stats -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $ticket_stats['open_tickets']; ?></h3>
                        <p>تذاكر مفتوحة</p>
                        <small class="text-muted">
                            إجمالي: <?php echo $ticket_stats['total_tickets']; ?> | 
                            مغلقة: <?php echo $ticket_stats['closed_tickets']; ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Orders -->
            <div class="col-lg-8 mb-4">
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-shopping-cart"></i>
                            آخر الطلبات
                        </h5>
                        <a href="orders/" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_orders)): ?>
                            <div class="empty-state">
                                <i class="fas fa-shopping-cart"></i>
                                <p>لا توجد طلبات حتى الآن</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>العميل</th>
                                            <th>الخطة</th>
                                            <th>الدومين</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td>
                                                    <a href="users/view.php?id=<?php echo $order['user_id']; ?>">
                                                        <?php echo htmlspecialchars($order['first_name'] . ' ' . $order['last_name']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo htmlspecialchars($order['plan_name']); ?></td>
                                                <td><?php echo htmlspecialchars($order['domain_name']); ?></td>
                                                <td><?php echo number_format($order['amount'], 2); ?> جنيه</td>
                                                <td>
                                                    <?php
                                                        $status_classes = [
                                                            'pending' => 'warning',
                                                            'processing' => 'info',
                                                            'active' => 'success',
                                                            'suspended' => 'danger',
                                                            'cancelled' => 'secondary'
                                                        ];
                                                        $status_labels = [
                                                            'pending' => 'معلق',
                                                            'processing' => 'قيد المعالجة',
                                                            'active' => 'نشط',
                                                            'suspended' => 'معلق',
                                                            'cancelled' => 'ملغي'
                                                        ];
                                                    ?>
                                                    <span class="badge badge-<?php echo $status_classes[$order['order_status']]; ?>">
                                                        <?php echo $status_labels[$order['order_status']]; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y/m/d', strtotime($order['created_at'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="col-lg-4 mb-4">
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-history"></i>
                            آخر الأنشطة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_activities)): ?>
                            <div class="empty-state">
                                <i class="fas fa-history"></i>
                                <p>لا توجد أنشطة حتى الآن</p>
                            </div>
                        <?php else: ?>
                            <div class="activity-list">
                                <?php foreach ($recent_activities as $activity): ?>
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p class="activity-description">
                                                <?php echo htmlspecialchars($activity['description']); ?>
                                            </p>
                                            <div class="activity-meta">
                                                <?php if ($activity['first_name']): ?>
                                                    <span class="activity-user">
                                                        <?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?>
                                                    </span>
                                                <?php endif; ?>
                                                <span class="activity-time">
                                                    <?php echo date('Y/m/d H:i', strtotime($activity['created_at'])); ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-bolt"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="users/" class="quick-action-card">
                                    <div class="quick-action-icon bg-primary">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h6>إدارة العملاء</h6>
                                </a>
                            </div>
                            
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="orders/" class="quick-action-card">
                                    <div class="quick-action-icon bg-success">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <h6>إدارة الطلبات</h6>
                                </a>
                            </div>
                            
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="billing/" class="quick-action-card">
                                    <div class="quick-action-icon bg-warning">
                                        <i class="fas fa-file-invoice-dollar"></i>
                                    </div>
                                    <h6>إدارة الفواتير</h6>
                                </a>
                            </div>
                            
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="support/" class="quick-action-card">
                                    <div class="quick-action-icon bg-info">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <h6>الدعم الفني</h6>
                                </a>
                            </div>
                            
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="plans/" class="quick-action-card">
                                    <div class="quick-action-icon bg-purple">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <h6>خطط الاستضافة</h6>
                                </a>
                            </div>
                            
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                <a href="settings/" class="quick-action-card">
                                    <div class="quick-action-icon bg-secondary">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <h6>الإعدادات</h6>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
