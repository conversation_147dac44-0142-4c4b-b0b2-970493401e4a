/* ملف الاستجابة للأجهزة المختلفة */

/* Extra Large Devices (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
}

/* Large Devices (992px and up) */
@media (min-width: 992px) and (max-width: 1399px) {
    .hero-title {
        font-size: 3.2rem;
    }
    
    .hero-features {
        justify-content: flex-start;
    }
}

/* Medium Devices (768px and up) */
@media (min-width: 768px) and (max-width: 991px) {
    .hero-section {
        padding: 100px 0 60px;
    }
    
    .hero-title {
        font-size: 2.8rem;
        margin-bottom: 1.2rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-features {
        gap: 1.5rem;
        margin-bottom: 2rem;
        justify-content: center;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .feature-card {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .contact-item {
        margin-bottom: 1rem;
    }
}

/* Small Devices (576px and up) */
@media (min-width: 576px) and (max-width: 767px) {
    .hero-section {
        padding: 90px 0 50px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.3;
    }
    
    .hero-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .feature-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .navbar-nav {
        text-align: center;
        margin-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
    
    .contact-info {
        padding: 1.5rem 0;
    }
    
    .contact-item {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
        text-align: center;
    }
}

/* Extra Small Devices (less than 576px) */
@media (max-width: 575px) {
    .hero-section {
        padding: 80px 0 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2rem;
        line-height: 1.4;
        margin-bottom: 1rem;
    }
    
    .hero-description {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }
    
    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }
    
    .feature-item {
        font-size: 0.9rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
        padding: 0 1rem;
    }
    
    .btn {
        width: 100%;
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
    }
    
    .btn-lg {
        padding: 0.875rem 2rem;
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
        margin-bottom: 2rem;
        padding: 0 1rem;
    }
    
    .feature-card {
        padding: 1.25rem;
        margin-bottom: 1.25rem;
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .feature-card h4 {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }
    
    .feature-card p {
        font-size: 0.9rem;
    }
    
    /* Navigation */
    .navbar {
        padding: 0.75rem 0;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .logo {
        height: 30px;
        margin-left: 8px;
    }
    
    .navbar-toggler {
        padding: 0.25rem 0.5rem;
        font-size: 1rem;
    }
    
    .navbar-nav {
        text-align: center;
        margin-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0.25rem 0;
        padding: 0.5rem 1rem !important;
        font-size: 0.95rem;
    }
    
    .dropdown-menu {
        margin-top: 0.25rem;
        min-width: 200px;
    }
    
    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    /* Contact Info */
    .contact-info {
        padding: 1rem 0;
    }
    
    .contact-item {
        flex-direction: column;
        gap: 0.25rem;
        margin-bottom: 0.75rem;
        text-align: center;
        font-size: 0.9rem;
    }
    
    .contact-item i {
        font-size: 1rem;
    }
}

/* Landscape Orientation for Mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .hero-section {
        padding: 60px 0 30px;
    }
    
    .hero-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .hero-features {
        margin-bottom: 1rem;
    }
    
    .hero-buttons {
        gap: 0.5rem;
    }
    
    .btn {
        padding: 0.5rem 1.25rem;
        font-size: 0.9rem;
    }
    
    .features-section {
        padding: 2rem 0 !important;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .feature-card {
        padding: 1rem;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .hero-section,
    .contact-info {
        display: none !important;
    }
    
    .features-section {
        page-break-inside: avoid;
    }
    
    .feature-card {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .feature-card:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
}
