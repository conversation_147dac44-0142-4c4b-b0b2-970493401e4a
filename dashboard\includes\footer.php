    </div> <!-- End Main Content -->
    
    <!-- Footer -->
    <footer class="dashboard-footer">
        <div class="footer-content">
            <div class="footer-left">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
            <div class="footer-right">
                <a href="../index.php">الموقع الرئيسي</a>
                <a href="support/new-ticket.php">الدعم الفني</a>
                <a href="<?php echo COMPANY_URL; ?>" target="_blank"><?php echo COMPANY_NAME; ?></a>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/dashboard.js"></script>
    
    <script>
        // تهيئة لوحة التحكم
        document.addEventListener('DOMContentLoaded', function() {
            // تبديل الشريط الجانبي
            const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const body = document.body;
            
            function toggleSidebar() {
                sidebar.classList.toggle('collapsed');
                body.classList.toggle('sidebar-collapsed');
            }
            
            if (sidebarToggleBtn) {
                sidebarToggleBtn.addEventListener('click', toggleSidebar);
            }
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }
            
            // القوائم الفرعية
            const submenuToggles = document.querySelectorAll('.has-submenu');
            submenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const submenu = this.nextElementSibling;
                    const arrow = this.querySelector('.submenu-arrow');
                    
                    if (submenu) {
                        submenu.classList.toggle('show');
                        arrow.classList.toggle('rotated');
                        
                        // إغلاق القوائم الفرعية الأخرى
                        submenuToggles.forEach(otherToggle => {
                            if (otherToggle !== this) {
                                const otherSubmenu = otherToggle.nextElementSibling;
                                const otherArrow = otherToggle.querySelector('.submenu-arrow');
                                if (otherSubmenu) {
                                    otherSubmenu.classList.remove('show');
                                    otherArrow.classList.remove('rotated');
                                }
                            }
                        });
                    }
                });
            });
            
            // تحديث الوقت
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const timeElement = document.getElementById('currentTime');
                if (timeElement) {
                    timeElement.textContent = timeString;
                }
            }
            
            // تحديث الوقت كل دقيقة
            updateTime();
            setInterval(updateTime, 60000);
            
            // تأكيد العمليات الحساسة
            const dangerButtons = document.querySelectorAll('.btn-danger[data-confirm]');
            dangerButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const message = this.getAttribute('data-confirm') || 'هل أنت متأكد من هذا الإجراء؟';
                    if (!confirm(message)) {
                        e.preventDefault();
                    }
                });
            });
            
            // تحسين تجربة المستخدم للجداول
            const tables = document.querySelectorAll('.table-responsive table');
            tables.forEach(table => {
                // إضافة تأثير التحديد للصفوف
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.addEventListener('click', function() {
                        // إزالة التحديد من الصفوف الأخرى
                        rows.forEach(r => r.classList.remove('selected'));
                        // إضافة التحديد للصف الحالي
                        this.classList.add('selected');
                    });
                });
            });
            
            // تحسين النماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = this.querySelector('[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                    }
                });
            });
            
            // إشعارات النجاح والخطأ
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                // إخفاء التنبيهات تلقائياً بعد 5 ثوان
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
            
            // تحسين الاستجابة للأجهزة المحمولة
            function handleMobileView() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.add('collapsed');
                    body.classList.add('sidebar-collapsed');
                } else {
                    sidebar.classList.remove('collapsed');
                    body.classList.remove('sidebar-collapsed');
                }
            }
            
            // تطبيق التحسينات عند تحميل الصفحة وتغيير حجم النافذة
            handleMobileView();
            window.addEventListener('resize', handleMobileView);
            
            // إضافة تأثيرات التحميل
            const loadingElements = document.querySelectorAll('[data-loading]');
            loadingElements.forEach(element => {
                element.addEventListener('click', function() {
                    this.classList.add('loading');
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                });
            });
            
            // تحسين البحث المباشر
            const searchInputs = document.querySelectorAll('.search-input');
            searchInputs.forEach(input => {
                let searchTimeout;
                
                input.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    
                    searchTimeout = setTimeout(() => {
                        const query = this.value.trim();
                        const targetTable = document.querySelector(this.getAttribute('data-target'));
                        
                        if (targetTable && query.length >= 2) {
                            filterTable(targetTable, query);
                        } else if (targetTable) {
                            showAllRows(targetTable);
                        }
                    }, 300);
                });
            });
            
            function filterTable(table, query) {
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(query.toLowerCase())) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            function showAllRows(table) {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.style.display = '';
                });
            }
            
            // تحسين التنقل بلوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                // Ctrl + / لفتح البحث
                if (e.ctrlKey && e.key === '/') {
                    e.preventDefault();
                    const searchInput = document.querySelector('.search-input');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
                
                // ESC لإغلاق النوافذ المنبثقة
                if (e.key === 'Escape') {
                    const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
                    openDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }
            });
            
            console.log('تم تحميل لوحة التحكم بنجاح - نقرة هوست');
        });
    </script>
</body>
</html>
