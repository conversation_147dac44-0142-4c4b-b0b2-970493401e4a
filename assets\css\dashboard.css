/* أنماط لوحة التحكم - نقرة هوست */

:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 70px;
    --sidebar-bg: #1e293b;
    --sidebar-text: #cbd5e1;
    --sidebar-active: #3b82f6;
    --dashboard-bg: #f8fafc;
}

/* Layout */
.dashboard-body {
    background-color: var(--dashboard-bg);
    font-family: var(--font-family);
    overflow-x: hidden;
}

.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.main-content {
    margin-right: var(--sidebar-width);
    min-height: 100vh;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.sidebar-collapsed .main-content {
    margin-right: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-logo img {
    height: 35px;
    width: auto;
}

.sidebar-brand {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--white-color);
    transition: all 0.3s ease;
}

.sidebar.collapsed .sidebar-brand {
    opacity: 0;
    width: 0;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--sidebar-text);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: none;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Sidebar Navigation */
.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--white-color);
}

.nav-link.active {
    background: var(--sidebar-active);
    color: var(--white-color);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--white-color);
}

.nav-link i {
    width: 20px;
    margin-left: 0.75rem;
    font-size: 1.1rem;
}

.nav-link span {
    transition: all 0.3s ease;
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
}

.submenu-arrow {
    margin-right: auto;
    transition: transform 0.3s ease;
}

.submenu-arrow.rotated {
    transform: rotate(180deg);
}

.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background: rgba(0, 0, 0, 0.2);
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.submenu.show {
    max-height: 300px;
    padding: 0.5rem 0;
}

.submenu li a {
    display: block;
    padding: 0.5rem 1.5rem 0.5rem 3.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.submenu li a:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--white-color);
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--sidebar-active);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 1.1rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.sidebar.collapsed .user-details {
    opacity: 0;
    width: 0;
}

.user-name {
    font-weight: 600;
    color: var(--white-color);
    font-size: 0.9rem;
}

.user-email {
    font-size: 0.8rem;
    color: var(--sidebar-text);
}

/* Top Navbar */
.top-navbar {
    background: var(--white-color);
    height: var(--topbar-height);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 999;
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 2rem;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: var(--gray-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background: var(--light-color);
    color: var(--dark-color);
}

.breadcrumb-nav {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.navbar-item {
    position: relative;
}

.navbar-btn {
    background: none;
    border: none;
    color: var(--gray-color);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-btn:hover {
    background: var(--light-color);
    color: var(--dark-color);
}

.notification-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--danger-color);
    color: var(--white-color);
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: var(--light-color);
}

.user-avatar-small {
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 0.9rem;
}

.user-name-small {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 2rem;
}

.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.welcome-actions {
    display: flex;
    gap: 1rem;
}

/* Statistics Cards */
.stat-card {
    background: var(--white-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 1.5rem;
}

.stat-icon.bg-primary { background: var(--primary-color); }
.stat-icon.bg-success { background: var(--success-color); }
.stat-icon.bg-warning { background: var(--warning-color); }
.stat-icon.bg-info { background: var(--accent-color); }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.stat-content p {
    color: var(--gray-color);
    margin: 0;
    font-weight: 500;
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Quick Actions */
.quick-action-card {
    display: block;
    background: var(--white-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
    text-align: center;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    color: var(--dark-color);
    text-decoration: none;
}

.quick-action-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    font-size: 1.2rem;
    margin: 0 auto 1rem;
}

.quick-action-card h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-action-card p {
    color: var(--gray-color);
    font-size: 0.9rem;
    margin: 0;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--gray-color);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Badges */
.badge {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-primary { background: var(--primary-color); color: var(--white-color); }
.badge-success { background: var(--success-color); color: var(--white-color); }
.badge-warning { background: var(--warning-color); color: var(--white-color); }
.badge-secondary { background: var(--gray-color); color: var(--white-color); }

/* Footer */
.dashboard-footer {
    background: var(--white-color);
    border-top: 1px solid #e5e7eb;
    padding: 1rem 2rem;
    margin-top: auto;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.footer-left p {
    color: var(--gray-color);
    margin: 0;
    font-size: 0.9rem;
}

.footer-right {
    display: flex;
    gap: 1.5rem;
}

.footer-right a {
    color: var(--gray-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.footer-right a:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: var(--sidebar-width);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .dashboard-content {
        padding: 1rem;
    }
    
    .navbar-content {
        padding: 0 1rem;
    }
    
    .welcome-section {
        padding: 1.5rem;
        text-align: center;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .welcome-actions {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
